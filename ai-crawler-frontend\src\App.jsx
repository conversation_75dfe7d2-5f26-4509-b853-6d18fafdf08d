import './App.css'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import NavBar from './components/NavBar-component'
import LandingPage from './components/landing-page-component'

function App() {
  return (
    <Router>
      <div className="App">
        <NavBar />
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/dashboard" element={<div className="min-h-screen bg-gray-900 text-white flex items-center justify-center"><h1 className="text-4xl">Dashboard Coming Soon</h1></div>} />
          <Route path="/pricing" element={<div className="min-h-screen bg-gray-900 text-white flex items-center justify-center"><h1 className="text-4xl">Pricing Coming Soon</h1></div>} />
          <Route path="/auth" element={<div className="min-h-screen bg-gray-900 text-white flex items-center justify-center"><h1 className="text-4xl">Authentication Coming Soon</h1></div>} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
