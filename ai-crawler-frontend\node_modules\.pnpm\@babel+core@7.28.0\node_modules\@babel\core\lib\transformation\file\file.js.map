{"version": 3, "names": ["helpers", "data", "require", "_traverse", "_codeFrame", "_t", "_semver", "_babel7Helpers", "cloneNode", "interpreterDirective", "errorVisitor", "enter", "path", "state", "loc", "node", "stop", "File", "constructor", "options", "code", "ast", "inputMap", "_map", "Map", "opts", "declarations", "scope", "metadata", "hub", "file", "getCode", "getScope", "addHelper", "bind", "buildError", "buildCodeFrameError", "NodePath", "get", "parentPath", "parent", "container", "key", "setContext", "shebang", "interpreter", "value", "replaceWith", "remove", "set", "val", "Error", "has", "availableHelper", "name", "versionRange", "isInternal", "minVersion", "err", "semver", "valid", "intersects", "_add<PERSON>elper", "declar", "generator", "res", "uid", "generateUidIdentifier", "dependencies", "dep", "getDependencies", "nodes", "globals", "Object", "keys", "getAllBindings", "for<PERSON>ach", "hasBinding", "rename", "_compact", "added", "unshiftContainer", "isVariableDeclaration", "registerDeclaration", "msg", "_Error", "SyntaxError", "traverse", "txt", "highlightCode", "codeFrameColumns", "start", "line", "column", "end", "undefined", "exports", "default", "prototype", "addImport", "addTemplateObject", "getModuleName", "babel7"], "sources": ["../../../src/transformation/file/file.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport { NodePath } from \"@babel/traverse\";\nimport type { HubInterface, Visitor, Scope } from \"@babel/traverse\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"@babel/traverse\";\nimport { cloneNode, interpreterDirective } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport semver from \"semver\";\n\nimport type { NormalizedFile } from \"../normalize-file.ts\";\n\n// @ts-expect-error This file is `any`\nimport babel7 from \"./babel-7-helpers.cjs\" with { if: \"!process.env.BABEL_8_BREAKING && (!USE_ESM || IS_STANDALONE)\" };\n\nconst errorVisitor: Visitor<{ loc: t.SourceLocation | null }> = {\n  enter(path, state) {\n    const loc = path.node.loc;\n    if (loc) {\n      state.loc = loc;\n      path.stop();\n    }\n  },\n};\n\nexport default class File {\n  _map: Map<unknown, unknown> = new Map();\n  opts: { [key: string]: any };\n  declarations: { [key: string]: t.Identifier } = {};\n  path: NodePath<t.Program>;\n  ast: t.File;\n  scope: Scope;\n  metadata: { [key: string]: any } = {};\n  code: string = \"\";\n  inputMap: any;\n\n  hub: HubInterface & { file: File } = {\n    // keep it for the usage in babel-core, ex: path.hub.file.opts.filename\n    file: this,\n    getCode: () => this.code,\n    getScope: () => this.scope,\n    addHelper: this.addHelper.bind(this),\n    buildError: this.buildCodeFrameError.bind(this),\n  };\n\n  constructor(options: any, { code, ast, inputMap }: NormalizedFile) {\n    this.opts = options;\n    this.code = code;\n    this.ast = ast;\n    this.inputMap = inputMap;\n\n    this.path = NodePath.get({\n      hub: this.hub,\n      parentPath: null,\n      parent: this.ast,\n      container: this.ast,\n      key: \"program\",\n    }).setContext() as NodePath<t.Program>;\n    this.scope = this.path.scope;\n  }\n\n  /**\n   * Provide backward-compatible access to the interpreter directive handling\n   * in Babel 6.x. If you are writing a plugin for Babel 7.x, it would be\n   * best to use 'program.interpreter' directly.\n   */\n  get shebang(): string {\n    const { interpreter } = this.path.node;\n    return interpreter ? interpreter.value : \"\";\n  }\n  set shebang(value: string) {\n    if (value) {\n      this.path.get(\"interpreter\").replaceWith(interpreterDirective(value));\n    } else {\n      this.path.get(\"interpreter\").remove();\n    }\n  }\n\n  set(key: unknown, val: unknown) {\n    if (!process.env.BABEL_8_BREAKING) {\n      if (key === \"helpersNamespace\") {\n        throw new Error(\n          \"Babel 7.0.0-beta.56 has dropped support for the 'helpersNamespace' utility.\" +\n            \"If you are using @babel/plugin-external-helpers you will need to use a newer \" +\n            \"version than the one you currently have installed. \" +\n            \"If you have your own implementation, you'll want to explore using 'helperGenerator' \" +\n            \"alongside 'file.availableHelper()'.\",\n        );\n      }\n    }\n\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  has(key: unknown): boolean {\n    return this._map.has(key);\n  }\n\n  /**\n   * Check if a given helper is available in @babel/core's helper list.\n   *\n   * This _also_ allows you to pass a Babel version specifically. If the\n   * helper exists, but was not available for the full given range, it will be\n   * considered unavailable.\n   */\n  availableHelper(name: string, versionRange?: string | null): boolean {\n    if (helpers.isInternal(name)) return false;\n\n    let minVersion;\n    try {\n      minVersion = helpers.minVersion(name);\n    } catch (err) {\n      if (err.code !== \"BABEL_HELPER_UNKNOWN\") throw err;\n\n      return false;\n    }\n\n    if (typeof versionRange !== \"string\") return true;\n\n    // semver.intersects() has some surprising behavior with comparing ranges\n    // with pre-release versions. We add '^' to ensure that we are always\n    // comparing ranges with ranges, which sidesteps this logic.\n    // For example:\n    //\n    //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n    //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n    //\n    // This is because the first falls back to\n    //\n    //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n    //\n    // and this fails because a prerelease version can only satisfy a range\n    // if it is a prerelease within the same major/minor/patch range.\n    //\n    // Note: If this is found to have issues, please also revisit the logic in\n    // transform-runtime's definitions.js file.\n    if (semver.valid(versionRange)) versionRange = `^${versionRange}`;\n\n    if (process.env.BABEL_8_BREAKING) {\n      return (\n        !semver.intersects(`<${minVersion}`, versionRange) &&\n        !semver.intersects(`>=9.0.0`, versionRange)\n      );\n    } else {\n      return (\n        !semver.intersects(`<${minVersion}`, versionRange) &&\n        !semver.intersects(`>=8.0.0`, versionRange)\n      );\n    }\n  }\n\n  addHelper(name: string): t.Identifier {\n    if (helpers.isInternal(name)) {\n      throw new Error(\"Cannot use internal helper \" + name);\n    }\n    return this._addHelper(name);\n  }\n\n  _addHelper(name: string): t.Identifier {\n    const declar = this.declarations[name];\n    if (declar) return cloneNode(declar);\n\n    const generator = this.get(\"helperGenerator\");\n    if (generator) {\n      const res = generator(name);\n      if (res) return res;\n    }\n\n    // make sure that the helper exists\n    helpers.minVersion(name);\n\n    const uid = (this.declarations[name] =\n      this.scope.generateUidIdentifier(name));\n\n    const dependencies: { [key: string]: t.Identifier } = {};\n    for (const dep of helpers.getDependencies(name)) {\n      dependencies[dep] = this._addHelper(dep);\n    }\n\n    const { nodes, globals } = helpers.get(\n      name,\n      dep => dependencies[dep],\n      uid.name,\n      Object.keys(this.scope.getAllBindings()),\n    );\n\n    globals.forEach(name => {\n      if (this.path.scope.hasBinding(name, true /* noGlobals */)) {\n        this.path.scope.rename(name);\n      }\n    });\n\n    nodes.forEach(node => {\n      // @ts-expect-error Fixme: document _compact node property\n      node._compact = true;\n    });\n\n    const added = this.path.unshiftContainer(\"body\", nodes);\n    // TODO: NodePath#unshiftContainer should automatically register new\n    // bindings.\n    for (const path of added) {\n      if (path.isVariableDeclaration()) this.scope.registerDeclaration(path);\n    }\n\n    return uid;\n  }\n\n  buildCodeFrameError(\n    node: t.Node | undefined | null,\n    msg: string,\n    _Error: typeof Error = SyntaxError,\n  ): Error {\n    let loc = node?.loc;\n\n    if (!loc && node) {\n      const state: { loc?: t.SourceLocation | null } = {\n        loc: null,\n      };\n      traverse(node, errorVisitor, this.scope, state);\n      loc = state.loc;\n\n      let txt =\n        \"This is an error on an internal node. Probably an internal error.\";\n      if (loc) txt += \" Location has been estimated.\";\n\n      msg += ` (${txt})`;\n    }\n\n    if (loc) {\n      const { highlightCode = true } = this.opts;\n\n      msg +=\n        \"\\n\" +\n        codeFrameColumns(\n          this.code,\n          {\n            start: {\n              line: loc.start.line,\n              column: loc.start.column + 1,\n            },\n            end:\n              loc.end && loc.start.line === loc.end.line\n                ? {\n                    line: loc.end.line,\n                    column: loc.end.column + 1,\n                  }\n                : undefined,\n          },\n          { highlightCode },\n        );\n    }\n\n    return new _Error(msg);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error Babel 7\n  File.prototype.addImport = function addImport() {\n    throw new Error(\n      \"This API has been removed. If you're looking for this \" +\n        \"functionality in Babel 7, you should import the \" +\n        \"'@babel/helper-module-imports' module and use the functions exposed \" +\n        \" from that module, such as 'addNamed' or 'addDefault'.\",\n    );\n  };\n  // @ts-expect-error Babel 7\n  File.prototype.addTemplateObject = function addTemplateObject() {\n    throw new Error(\n      \"This function has been moved into the template literal transform itself.\",\n    );\n  };\n\n  if (!USE_ESM || IS_STANDALONE) {\n    // @ts-expect-error Babel 7\n    File.prototype.getModuleName = function getModuleName() {\n      return babel7.getModuleName()(this.opts, this.opts);\n    };\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,GAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,EAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,IAAAM,cAAA,GAAAL,OAAA;AAAuH;EAP9GM,SAAS;EAAEC;AAAoB,IAAAJ,EAAA;AASxC,MAAMK,YAAuD,GAAG;EAC9DC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAGF,IAAI,CAACG,IAAI,CAACD,GAAG;IACzB,IAAIA,GAAG,EAAE;MACPD,KAAK,CAACC,GAAG,GAAGA,GAAG;MACfF,IAAI,CAACI,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEc,MAAMC,IAAI,CAAC;EAoBxBC,WAAWA,CAACC,OAAY,EAAE;IAAEC,IAAI;IAAEC,GAAG;IAAEC;EAAyB,CAAC,EAAE;IAAA,KAnBnEC,IAAI,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCC,IAAI;IAAA,KACJC,YAAY,GAAoC,CAAC,CAAC;IAAA,KAClDd,IAAI;IAAA,KACJS,GAAG;IAAA,KACHM,KAAK;IAAA,KACLC,QAAQ,GAA2B,CAAC,CAAC;IAAA,KACrCR,IAAI,GAAW,EAAE;IAAA,KACjBE,QAAQ;IAAA,KAERO,GAAG,GAAkC;MAEnCC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACX,IAAI;MACxBY,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACL,KAAK;MAC1BM,SAAS,EAAE,IAAI,CAACA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;MACpCC,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACF,IAAI,CAAC,IAAI;IAChD,CAAC;IAGC,IAAI,CAACT,IAAI,GAAGN,OAAO;IACnB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACV,IAAI,GAAGyB,oBAAQ,CAACC,GAAG,CAAC;MACvBT,GAAG,EAAE,IAAI,CAACA,GAAG;MACbU,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI,CAACnB,GAAG;MAChBoB,SAAS,EAAE,IAAI,CAACpB,GAAG;MACnBqB,GAAG,EAAE;IACP,CAAC,CAAC,CAACC,UAAU,CAAC,CAAwB;IACtC,IAAI,CAAChB,KAAK,GAAG,IAAI,CAACf,IAAI,CAACe,KAAK;EAC9B;EAOA,IAAIiB,OAAOA,CAAA,EAAW;IACpB,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACjC,IAAI,CAACG,IAAI;IACtC,OAAO8B,WAAW,GAAGA,WAAW,CAACC,KAAK,GAAG,EAAE;EAC7C;EACA,IAAIF,OAAOA,CAACE,KAAa,EAAE;IACzB,IAAIA,KAAK,EAAE;MACT,IAAI,CAAClC,IAAI,CAAC0B,GAAG,CAAC,aAAa,CAAC,CAACS,WAAW,CAACtC,oBAAoB,CAACqC,KAAK,CAAC,CAAC;IACvE,CAAC,MAAM;MACL,IAAI,CAAClC,IAAI,CAAC0B,GAAG,CAAC,aAAa,CAAC,CAACU,MAAM,CAAC,CAAC;IACvC;EACF;EAEAC,GAAGA,CAACP,GAAY,EAAEQ,GAAY,EAAE;IACK;MACjC,IAAIR,GAAG,KAAK,kBAAkB,EAAE;QAC9B,MAAM,IAAIS,KAAK,CACb,6EAA6E,GAC3E,+EAA+E,GAC/E,qDAAqD,GACrD,sFAAsF,GACtF,qCACJ,CAAC;MACH;IACF;IAEA,IAAI,CAAC5B,IAAI,CAAC0B,GAAG,CAACP,GAAG,EAAEQ,GAAG,CAAC;EACzB;EAEAZ,GAAGA,CAACI,GAAY,EAAO;IACrB,OAAO,IAAI,CAACnB,IAAI,CAACe,GAAG,CAACI,GAAG,CAAC;EAC3B;EAEAU,GAAGA,CAACV,GAAY,EAAW;IACzB,OAAO,IAAI,CAACnB,IAAI,CAAC6B,GAAG,CAACV,GAAG,CAAC;EAC3B;EASAW,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAW;IACnE,IAAIvD,OAAO,CAAD,CAAC,CAACwD,UAAU,CAACF,IAAI,CAAC,EAAE,OAAO,KAAK;IAE1C,IAAIG,UAAU;IACd,IAAI;MACFA,UAAU,GAAGzD,OAAO,CAAD,CAAC,CAACyD,UAAU,CAACH,IAAI,CAAC;IACvC,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ,IAAIA,GAAG,CAACtC,IAAI,KAAK,sBAAsB,EAAE,MAAMsC,GAAG;MAElD,OAAO,KAAK;IACd;IAEA,IAAI,OAAOH,YAAY,KAAK,QAAQ,EAAE,OAAO,IAAI;IAmBjD,IAAII,QAAKA,CAAC,CAACC,KAAK,CAACL,YAAY,CAAC,EAAEA,YAAY,GAAG,IAAIA,YAAY,EAAE;IAO1D;MACL,OACE,CAACI,QAAKA,CAAC,CAACE,UAAU,CAAC,IAAIJ,UAAU,EAAE,EAAEF,YAAY,CAAC,IAClD,CAACI,QAAKA,CAAC,CAACE,UAAU,CAAC,SAAS,EAAEN,YAAY,CAAC;IAE/C;EACF;EAEAtB,SAASA,CAACqB,IAAY,EAAgB;IACpC,IAAItD,OAAO,CAAD,CAAC,CAACwD,UAAU,CAACF,IAAI,CAAC,EAAE;MAC5B,MAAM,IAAIH,KAAK,CAAC,6BAA6B,GAAGG,IAAI,CAAC;IACvD;IACA,OAAO,IAAI,CAACQ,UAAU,CAACR,IAAI,CAAC;EAC9B;EAEAQ,UAAUA,CAACR,IAAY,EAAgB;IACrC,MAAMS,MAAM,GAAG,IAAI,CAACrC,YAAY,CAAC4B,IAAI,CAAC;IACtC,IAAIS,MAAM,EAAE,OAAOvD,SAAS,CAACuD,MAAM,CAAC;IAEpC,MAAMC,SAAS,GAAG,IAAI,CAAC1B,GAAG,CAAC,iBAAiB,CAAC;IAC7C,IAAI0B,SAAS,EAAE;MACb,MAAMC,GAAG,GAAGD,SAAS,CAACV,IAAI,CAAC;MAC3B,IAAIW,GAAG,EAAE,OAAOA,GAAG;IACrB;IAGAjE,OAAO,CAAD,CAAC,CAACyD,UAAU,CAACH,IAAI,CAAC;IAExB,MAAMY,GAAG,GAAI,IAAI,CAACxC,YAAY,CAAC4B,IAAI,CAAC,GAClC,IAAI,CAAC3B,KAAK,CAACwC,qBAAqB,CAACb,IAAI,CAAE;IAEzC,MAAMc,YAA6C,GAAG,CAAC,CAAC;IACxD,KAAK,MAAMC,GAAG,IAAIrE,OAAO,CAAD,CAAC,CAACsE,eAAe,CAAChB,IAAI,CAAC,EAAE;MAC/Cc,YAAY,CAACC,GAAG,CAAC,GAAG,IAAI,CAACP,UAAU,CAACO,GAAG,CAAC;IAC1C;IAEA,MAAM;MAAEE,KAAK;MAAEC;IAAQ,CAAC,GAAGxE,OAAO,CAAD,CAAC,CAACsC,GAAG,CACpCgB,IAAI,EACJe,GAAG,IAAID,YAAY,CAACC,GAAG,CAAC,EACxBH,GAAG,CAACZ,IAAI,EACRmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/C,KAAK,CAACgD,cAAc,CAAC,CAAC,CACzC,CAAC;IAEDH,OAAO,CAACI,OAAO,CAACtB,IAAI,IAAI;MACtB,IAAI,IAAI,CAAC1C,IAAI,CAACe,KAAK,CAACkD,UAAU,CAACvB,IAAI,EAAE,IAAoB,CAAC,EAAE;QAC1D,IAAI,CAAC1C,IAAI,CAACe,KAAK,CAACmD,MAAM,CAACxB,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC;IAEFiB,KAAK,CAACK,OAAO,CAAC7D,IAAI,IAAI;MAEpBA,IAAI,CAACgE,QAAQ,GAAG,IAAI;IACtB,CAAC,CAAC;IAEF,MAAMC,KAAK,GAAG,IAAI,CAACpE,IAAI,CAACqE,gBAAgB,CAAC,MAAM,EAAEV,KAAK,CAAC;IAGvD,KAAK,MAAM3D,IAAI,IAAIoE,KAAK,EAAE;MACxB,IAAIpE,IAAI,CAACsE,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACvD,KAAK,CAACwD,mBAAmB,CAACvE,IAAI,CAAC;IACxE;IAEA,OAAOsD,GAAG;EACZ;EAEA9B,mBAAmBA,CACjBrB,IAA+B,EAC/BqE,GAAW,EACXC,MAAoB,GAAGC,WAAW,EAC3B;IACP,IAAIxE,GAAG,GAAGC,IAAI,oBAAJA,IAAI,CAAED,GAAG;IAEnB,IAAI,CAACA,GAAG,IAAIC,IAAI,EAAE;MAChB,MAAMF,KAAwC,GAAG;QAC/CC,GAAG,EAAE;MACP,CAAC;MACD,IAAAyE,mBAAQ,EAACxE,IAAI,EAAEL,YAAY,EAAE,IAAI,CAACiB,KAAK,EAAEd,KAAK,CAAC;MAC/CC,GAAG,GAAGD,KAAK,CAACC,GAAG;MAEf,IAAI0E,GAAG,GACL,mEAAmE;MACrE,IAAI1E,GAAG,EAAE0E,GAAG,IAAI,+BAA+B;MAE/CJ,GAAG,IAAI,KAAKI,GAAG,GAAG;IACpB;IAEA,IAAI1E,GAAG,EAAE;MACP,MAAM;QAAE2E,aAAa,GAAG;MAAK,CAAC,GAAG,IAAI,CAAChE,IAAI;MAE1C2D,GAAG,IACD,IAAI,GACJ,IAAAM,6BAAgB,EACd,IAAI,CAACtE,IAAI,EACT;QACEuE,KAAK,EAAE;UACLC,IAAI,EAAE9E,GAAG,CAAC6E,KAAK,CAACC,IAAI;UACpBC,MAAM,EAAE/E,GAAG,CAAC6E,KAAK,CAACE,MAAM,GAAG;QAC7B,CAAC;QACDC,GAAG,EACDhF,GAAG,CAACgF,GAAG,IAAIhF,GAAG,CAAC6E,KAAK,CAACC,IAAI,KAAK9E,GAAG,CAACgF,GAAG,CAACF,IAAI,GACtC;UACEA,IAAI,EAAE9E,GAAG,CAACgF,GAAG,CAACF,IAAI;UAClBC,MAAM,EAAE/E,GAAG,CAACgF,GAAG,CAACD,MAAM,GAAG;QAC3B,CAAC,GACDE;MACR,CAAC,EACD;QAAEN;MAAc,CAClB,CAAC;IACL;IAEA,OAAO,IAAIJ,MAAM,CAACD,GAAG,CAAC;EACxB;AACF;AAACY,OAAA,CAAAC,OAAA,GAAAhF,IAAA;AAEkC;EAEjCA,IAAI,CAACiF,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IAC9C,MAAM,IAAIhD,KAAK,CACb,wDAAwD,GACtD,kDAAkD,GAClD,sEAAsE,GACtE,wDACJ,CAAC;EACH,CAAC;EAEDlC,IAAI,CAACiF,SAAS,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IAC9D,MAAM,IAAIjD,KAAK,CACb,0EACF,CAAC;EACH,CAAC;EAE8B;IAE7BlC,IAAI,CAACiF,SAAS,CAACG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;MACtD,OAAOC,cAAM,CAACD,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC5E,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC;IACrD,CAAC;EACH;AACF;AAAC", "ignoreList": []}