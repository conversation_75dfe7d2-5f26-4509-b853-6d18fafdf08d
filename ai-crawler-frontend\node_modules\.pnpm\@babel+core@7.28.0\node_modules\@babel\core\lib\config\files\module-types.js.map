{"version": 3, "names": ["_async", "require", "_path", "data", "_url", "_semver", "_debug", "_rewriteStackTrace", "_configError", "_transformFile", "asyncGeneratorStep", "n", "t", "e", "r", "o", "a", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "debug", "buildDebug", "import_", "_unused", "supportsESM", "exports", "semver", "satisfies", "process", "versions", "node", "LOADING_CJS_FILES", "Set", "loadCjsDefault", "filepath", "has", "module", "add", "endHiddenCallStack", "delete", "__esModule", "Symbol", "toStringTag", "default", "undefined", "loadMjsFromPath", "_loadMjsFromPath", "url", "pathToFileURL", "toString", "ConfigError", "_x", "tsNotSupportedError", "ext", "SUPPORTED_EXTENSIONS", "asyncModules", "loadCodeDefault", "loader", "esmError", "tlaE<PERSON>r", "async", "path", "extname", "isTS", "type", "hasOwnProperty", "call", "pattern", "ensureTsSupport", "code", "isAsync", "promise", "waitFor", "Error", "callback", "features", "typescript", "extensions", "opts", "babelrc", "configFile", "sourceType", "sourceMaps", "sourceFileName", "basename", "presets", "getTSPreset", "Object", "assign", "onlyRemoveTypeImports", "optimizeConstEnums", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "m", "filename", "endsWith", "_compile", "transformFileSync", "error", "packageJson", "lt", "version", "console", "message", "pnp"], "sources": ["../../../src/config/files/module-types.ts"], "sourcesContent": ["import { isAsync, waitFor } from \"../../gensync-utils/async.ts\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport path from \"node:path\";\nimport { pathToFileURL } from \"node:url\";\nimport { createRequire } from \"node:module\";\nimport semver from \"semver\";\nimport buildDebug from \"debug\";\n\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace.ts\";\nimport ConfigError from \"../../errors/config-error.ts\";\n\nimport type { InputOptions } from \"../index.ts\";\nimport { transformFileSync } from \"../../transform-file.ts\";\n\nconst debug = buildDebug(\"babel:config:loading:files:module-types\");\n\nconst require = createRequire(import.meta.url);\n\nif (!process.env.BABEL_8_BREAKING) {\n  try {\n    // Old Node.js versions don't support import() syntax.\n    // eslint-disable-next-line no-var\n    var import_:\n      | ((specifier: string | URL) => any)\n      | undefined = require(\"./import.cjs\");\n  } catch {}\n}\n\nexport const supportsESM = semver.satisfies(\n  process.versions.node,\n  // older versions, starting from 10, support the dynamic\n  // import syntax but always return a rejected promise.\n  \"^12.17 || >=13.2\",\n);\n\nconst LOADING_CJS_FILES = new Set();\n\nfunction loadCjsDefault(filepath: string) {\n  // The `require()` call below can make this code reentrant if a require hook\n  // like @babel/register has been loaded into the system. That would cause\n  // Babel to attempt to compile the `.babelrc.js` file as it loads below. To\n  // cover this case, we auto-ignore re-entrant config processing. ESM loaders\n  // do not have this problem, because loaders do not apply to themselves.\n  if (LOADING_CJS_FILES.has(filepath)) {\n    debug(\"Auto-ignoring usage of config %o.\", filepath);\n    return {};\n  }\n\n  let module;\n  try {\n    LOADING_CJS_FILES.add(filepath);\n    module = endHiddenCallStack(require)(filepath);\n  } finally {\n    LOADING_CJS_FILES.delete(filepath);\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    return module != null &&\n      (module.__esModule || module[Symbol.toStringTag] === \"Module\")\n      ? module.default\n      : module;\n  } else {\n    return module != null &&\n      (module.__esModule || module[Symbol.toStringTag] === \"Module\")\n      ? module.default ||\n          /* fallbackToTranspiledModule */ (arguments[1] ? module : undefined)\n      : module;\n  }\n}\n\nconst loadMjsFromPath = endHiddenCallStack(async function loadMjsFromPath(\n  filepath: string,\n) {\n  // Add ?import as a workaround for https://github.com/nodejs/node/issues/55500\n  const url = pathToFileURL(filepath).toString() + \"?import\";\n\n  if (process.env.BABEL_8_BREAKING) {\n    return await import(url);\n  } else {\n    if (!import_) {\n      throw new ConfigError(\n        \"Internal error: Native ECMAScript modules aren't supported by this platform.\\n\",\n        filepath,\n      );\n    }\n\n    return await import_(url);\n  }\n});\n\nconst tsNotSupportedError = (ext: string) => `\\\nYou are using a ${ext} config file, but Babel only supports transpiling .cts configs. Either:\n- Use a .cts config file\n- Update to Node.js 23.6.0, which has native TypeScript support\n- Install tsx to transpile ${ext} files on the fly\\\n`;\n\nconst SUPPORTED_EXTENSIONS = {\n  \".js\": \"unknown\",\n  \".mjs\": \"esm\",\n  \".cjs\": \"cjs\",\n  \".ts\": \"unknown\",\n  \".mts\": \"esm\",\n  \".cts\": \"cjs\",\n} as const;\n\nconst asyncModules = new Set();\n\nexport default function* loadCodeDefault(\n  filepath: string,\n  loader: \"require\" | \"auto\",\n  esmError: string,\n  tlaError: string,\n): Handler<unknown> {\n  let async;\n\n  const ext = path.extname(filepath);\n  const isTS = ext === \".ts\" || ext === \".cts\" || ext === \".mts\";\n\n  const type =\n    SUPPORTED_EXTENSIONS[\n      Object.hasOwn(SUPPORTED_EXTENSIONS, ext)\n        ? (ext as keyof typeof SUPPORTED_EXTENSIONS)\n        : (\".js\" as const)\n    ];\n\n  const pattern = `${loader} ${type}` as const;\n  switch (pattern) {\n    case \"require cjs\":\n    case \"auto cjs\":\n      if (isTS) {\n        return ensureTsSupport(filepath, ext, () => loadCjsDefault(filepath));\n      } else if (process.env.BABEL_8_BREAKING) {\n        return loadCjsDefault(filepath);\n      } else {\n        return loadCjsDefault(\n          filepath,\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          /* fallbackToTranspiledModule */ arguments[2],\n        );\n      }\n    case \"auto unknown\":\n    case \"require unknown\":\n    case \"require esm\":\n      try {\n        if (isTS) {\n          return ensureTsSupport(filepath, ext, () => loadCjsDefault(filepath));\n        } else if (process.env.BABEL_8_BREAKING) {\n          return loadCjsDefault(filepath);\n        } else {\n          return loadCjsDefault(\n            filepath,\n            // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n            /* fallbackToTranspiledModule */ arguments[2],\n          );\n        }\n      } catch (e) {\n        if (\n          e.code === \"ERR_REQUIRE_ASYNC_MODULE\" ||\n          // Node.js 13.0.0 throws ERR_REQUIRE_CYCLE_MODULE instead of\n          // ERR_REQUIRE_ASYNC_MODULE when requiring a module a second time\n          // https://github.com/nodejs/node/issues/55516\n          // This `asyncModules` won't catch all of such cases, but it will\n          // at least catch those caused by Babel trying to load a module twice.\n          (e.code === \"ERR_REQUIRE_CYCLE_MODULE\" && asyncModules.has(filepath))\n        ) {\n          asyncModules.add(filepath);\n          if (!(async ??= yield* isAsync())) {\n            throw new ConfigError(tlaError, filepath);\n          }\n          // fall through: require() failed due to TLA\n        } else if (\n          e.code === \"ERR_REQUIRE_ESM\" ||\n          (!process.env.BABEL_8_BREAKING && type === \"esm\")\n        ) {\n          // fall through: require() failed due to ESM\n        } else {\n          throw e;\n        }\n      }\n    // fall through: require() failed due to ESM or TLA, try import()\n    case \"auto esm\":\n      if ((async ??= yield* isAsync())) {\n        const promise = isTS\n          ? ensureTsSupport(filepath, ext, () => loadMjsFromPath(filepath))\n          : loadMjsFromPath(filepath);\n\n        return (yield* waitFor(promise)).default;\n      }\n      if (isTS) {\n        throw new ConfigError(tsNotSupportedError(ext), filepath);\n      } else {\n        throw new ConfigError(esmError, filepath);\n      }\n    default:\n      throw new Error(\"Internal Babel error: unreachable code.\");\n  }\n}\n\nfunction ensureTsSupport<T>(\n  filepath: string,\n  ext: string,\n  callback: () => T,\n): T {\n  if (\n    process.features.typescript ||\n    require.extensions[\".ts\"] ||\n    require.extensions[\".cts\"] ||\n    require.extensions[\".mts\"]\n  ) {\n    return callback();\n  }\n\n  if (ext !== \".cts\") {\n    throw new ConfigError(tsNotSupportedError(ext), filepath);\n  }\n\n  const opts: InputOptions = {\n    babelrc: false,\n    configFile: false,\n    sourceType: \"unambiguous\",\n    sourceMaps: \"inline\",\n    sourceFileName: path.basename(filepath),\n    presets: [\n      [\n        getTSPreset(filepath),\n        {\n          onlyRemoveTypeImports: true,\n          optimizeConstEnums: true,\n          ...(process.env.BABEL_8_BREAKING ? {} : { allowDeclareFields: true }),\n        },\n      ],\n    ],\n  };\n\n  let handler: NodeJS.RequireExtensions[\"\"] = function (m, filename) {\n    // If we want to support `.ts`, `.d.ts` must be handled specially.\n    if (handler && filename.endsWith(\".cts\")) {\n      try {\n        // @ts-expect-error Undocumented API\n        return m._compile(\n          transformFileSync(filename, {\n            ...opts,\n            filename,\n          }).code,\n          filename,\n        );\n      } catch (error) {\n        // TODO(Babel 8): Add this as an optional peer dependency\n        // eslint-disable-next-line import/no-extraneous-dependencies\n        const packageJson = require(\"@babel/preset-typescript/package.json\");\n        if (semver.lt(packageJson.version, \"7.21.4\")) {\n          console.error(\n            \"`.cts` configuration file failed to load, please try to update `@babel/preset-typescript`.\",\n          );\n        }\n        throw error;\n      }\n    }\n    return require.extensions[\".js\"](m, filename);\n  };\n  require.extensions[ext] = handler;\n\n  try {\n    return callback();\n  } finally {\n    if (require.extensions[ext] === handler) delete require.extensions[ext];\n    handler = undefined;\n  }\n}\n\nfunction getTSPreset(filepath: string) {\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require(\"@babel/preset-typescript\");\n  } catch (error) {\n    if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n    let message =\n      \"You appear to be using a .cts file as Babel configuration, but the `@babel/preset-typescript` package was not found: please install it!\";\n\n    if (!process.env.BABEL_8_BREAKING) {\n      if (process.versions.pnp) {\n        // Using Yarn PnP, which doesn't allow requiring packages that are not\n        // explicitly specified as dependencies.\n        message += `\nIf you are using Yarn Plug'n'Play, you may also need to add the following configuration to your .yarnrc.yml file:\n\npackageExtensions:\n\\t\"@babel/core@*\":\n\\t\\tpeerDependencies:\n\\t\\t\\t\"@babel/preset-typescript\": \"*\"\n`;\n      }\n    }\n\n    throw new ConfigError(message, filepath);\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,SAAAC,MAAA;EAAA,MAAAC,IAAA,GAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAC,KAAA;EAAA,MAAAD,IAAA,GAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAD,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACAF,OAAA;AACA,SAAAI,QAAA;EAAA,MAAAF,IAAA,GAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAF,OAAA;EAAAK,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAI,kBAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAGA,IAAAQ,cAAA,GAAAR,OAAA;AAA4D,SAAAS,mBAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,cAAAC,CAAA,GAAAP,CAAA,CAAAK,CAAA,EAAAC,CAAA,GAAAE,CAAA,GAAAD,CAAA,CAAAE,KAAA,WAAAT,CAAA,gBAAAE,CAAA,CAAAF,CAAA,KAAAO,CAAA,CAAAG,IAAA,GAAAT,CAAA,CAAAO,CAAA,IAAAG,OAAA,CAAAC,OAAA,CAAAJ,CAAA,EAAAK,IAAA,CAAAV,CAAA,EAAAC,CAAA;AAAA,SAAAU,kBAAAd,CAAA,6BAAAC,CAAA,SAAAC,CAAA,GAAAa,SAAA,aAAAJ,OAAA,WAAAR,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,CAAA,CAAAgB,KAAA,CAAAf,CAAA,EAAAC,CAAA,YAAAe,MAAAjB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,UAAAlB,CAAA,cAAAkB,OAAAlB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,WAAAlB,CAAA,KAAAiB,KAAA;AAE5D,MAAME,KAAK,GAAGC,OAASA,CAAC,CAAC,yCAAyC,CAAC;AAIhC;EACjC,IAAI;IAGF,IAAIC,OAES,GAAG/B,OAAO,CAAC,cAAc,CAAC;EACzC,CAAC,CAAC,OAAAgC,OAAA,EAAM,CAAC;AACX;AAEO,MAAMC,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAGE,QAAKA,CAAC,CAACC,SAAS,CACzCC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAGrB,kBACF,CAAC;AAED,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEnC,SAASC,cAAcA,CAACC,QAAgB,EAAE;EAMxC,IAAIH,iBAAiB,CAACI,GAAG,CAACD,QAAQ,CAAC,EAAE;IACnCd,KAAK,CAAC,mCAAmC,EAAEc,QAAQ,CAAC;IACpD,OAAO,CAAC,CAAC;EACX;EAEA,IAAIE,MAAM;EACV,IAAI;IACFL,iBAAiB,CAACM,GAAG,CAACH,QAAQ,CAAC;IAC/BE,MAAM,GAAG,IAAAE,qCAAkB,EAAC/C,OAAO,CAAC,CAAC2C,QAAQ,CAAC;EAChD,CAAC,SAAS;IACRH,iBAAiB,CAACQ,MAAM,CAACL,QAAQ,CAAC;EACpC;EAOO;IACL,OAAOE,MAAM,IAAI,IAAI,KAClBA,MAAM,CAACI,UAAU,IAAIJ,MAAM,CAACK,MAAM,CAACC,WAAW,CAAC,KAAK,QAAQ,CAAC,GAC5DN,MAAM,CAACO,OAAO,KACsB3B,SAAS,CAAC,CAAC,CAAC,GAAGoB,MAAM,GAAGQ,SAAS,CAAC,GACtER,MAAM;EACZ;AACF;AAEA,MAAMS,eAAe,GAAG,IAAAP,qCAAkB;EAAA,IAAAQ,gBAAA,GAAA/B,iBAAA,CAAC,WACzCmB,QAAgB,EAChB;IAEA,MAAMa,GAAG,GAAG,IAAAC,oBAAa,EAACd,QAAQ,CAAC,CAACe,QAAQ,CAAC,CAAC,GAAG,SAAS;IAInD;MACL,IAAI,CAAC3B,OAAO,EAAE;QACZ,MAAM,IAAI4B,oBAAW,CACnB,gFAAgF,EAChFhB,QACF,CAAC;MACH;MAEA,aAAaZ,OAAO,CAACyB,GAAG,CAAC;IAC3B;EACF,CAAC;EAAA,SAlByDF,eAAeA,CAAAM,EAAA;IAAA,OAAAL,gBAAA,CAAA7B,KAAA,OAAAD,SAAA;EAAA;EAAA,OAAf6B,eAAe;AAAA,GAkBxE,CAAC;AAEF,MAAMO,mBAAmB,GAAIC,GAAW,IAAK;AAC7C,kBAAkBA,GAAG;AACrB;AACA;AACA,6BAA6BA,GAAG;AAChC,CAAC;AAED,MAAMC,oBAAoB,GAAG;EAC3B,KAAK,EAAE,SAAS;EAChB,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,KAAK;EACb,KAAK,EAAE,SAAS;EAChB,MAAM,EAAE,KAAK;EACb,MAAM,EAAE;AACV,CAAU;AAEV,MAAMC,YAAY,GAAG,IAAIvB,GAAG,CAAC,CAAC;AAEf,UAAUwB,eAAeA,CACtCtB,QAAgB,EAChBuB,MAA0B,EAC1BC,QAAgB,EAChBC,QAAgB,EACE;EAClB,IAAIC,KAAK;EAET,MAAMP,GAAG,GAAGQ,MAAGA,CAAC,CAACC,OAAO,CAAC5B,QAAQ,CAAC;EAClC,MAAM6B,IAAI,GAAGV,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,MAAM;EAE9D,MAAMW,IAAI,GACRV,oBAAoB,CAClBW,cAAA,CAAAC,IAAA,CAAcZ,oBAAoB,EAAED,GAAG,CAAC,GACnCA,GAAG,GACH,KAAe,CACrB;EAEH,MAAMc,OAAO,GAAG,GAAGV,MAAM,IAAIO,IAAI,EAAW;EAC5C,QAAQG,OAAO;IACb,KAAK,aAAa;IAClB,KAAK,UAAU;MACb,IAAIJ,IAAI,EAAE;QACR,OAAOK,eAAe,CAAClC,QAAQ,EAAEmB,GAAG,EAAE,MAAMpB,cAAc,CAACC,QAAQ,CAAC,CAAC;MACvE,CAAC,MAEM;QACL,OAAOD,cAAc,CACnBC,QAAQ,EAEyBlB,SAAS,CAAC,CAAC,CAC9C,CAAC;MACH;IACF,KAAK,cAAc;IACnB,KAAK,iBAAiB;IACtB,KAAK,aAAa;MAChB,IAAI;QACF,IAAI+C,IAAI,EAAE;UACR,OAAOK,eAAe,CAAClC,QAAQ,EAAEmB,GAAG,EAAE,MAAMpB,cAAc,CAACC,QAAQ,CAAC,CAAC;QACvE,CAAC,MAEM;UACL,OAAOD,cAAc,CACnBC,QAAQ,EAEyBlB,SAAS,CAAC,CAAC,CAC9C,CAAC;QACH;MACF,CAAC,CAAC,OAAOb,CAAC,EAAE;QACV,IACEA,CAAC,CAACkE,IAAI,KAAK,0BAA0B,IAMpClE,CAAC,CAACkE,IAAI,KAAK,0BAA0B,IAAId,YAAY,CAACpB,GAAG,CAACD,QAAQ,CAAE,EACrE;UACAqB,YAAY,CAAClB,GAAG,CAACH,QAAQ,CAAC;UAC1B,IAAI,EAAE0B,KAAK,WAALA,KAAK,GAALA,KAAK,GAAK,OAAO,IAAAU,cAAO,EAAC,CAAC,CAAC,EAAE;YACjC,MAAM,IAAIpB,oBAAW,CAACS,QAAQ,EAAEzB,QAAQ,CAAC;UAC3C;QAEF,CAAC,MAAM,IACL/B,CAAC,CAACkE,IAAI,KAAK,iBAAiB,IACML,IAAI,KAAK,KAAK,EAChD,CAEF,CAAC,MAAM;UACL,MAAM7D,CAAC;QACT;MACF;IAEF,KAAK,UAAU;MACb,IAAKyD,KAAK,WAALA,KAAK,GAALA,KAAK,GAAK,OAAO,IAAAU,cAAO,EAAC,CAAC,EAAG;QAChC,MAAMC,OAAO,GAAGR,IAAI,GAChBK,eAAe,CAAClC,QAAQ,EAAEmB,GAAG,EAAE,MAAMR,eAAe,CAACX,QAAQ,CAAC,CAAC,GAC/DW,eAAe,CAACX,QAAQ,CAAC;QAE7B,OAAO,CAAC,OAAO,IAAAsC,cAAO,EAACD,OAAO,CAAC,EAAE5B,OAAO;MAC1C;MACA,IAAIoB,IAAI,EAAE;QACR,MAAM,IAAIb,oBAAW,CAACE,mBAAmB,CAACC,GAAG,CAAC,EAAEnB,QAAQ,CAAC;MAC3D,CAAC,MAAM;QACL,MAAM,IAAIgB,oBAAW,CAACQ,QAAQ,EAAExB,QAAQ,CAAC;MAC3C;IACF;MACE,MAAM,IAAIuC,KAAK,CAAC,yCAAyC,CAAC;EAC9D;AACF;AAEA,SAASL,eAAeA,CACtBlC,QAAgB,EAChBmB,GAAW,EACXqB,QAAiB,EACd;EACH,IACE9C,OAAO,CAAC+C,QAAQ,CAACC,UAAU,IAC3BrF,OAAO,CAACsF,UAAU,CAAC,KAAK,CAAC,IACzBtF,OAAO,CAACsF,UAAU,CAAC,MAAM,CAAC,IAC1BtF,OAAO,CAACsF,UAAU,CAAC,MAAM,CAAC,EAC1B;IACA,OAAOH,QAAQ,CAAC,CAAC;EACnB;EAEA,IAAIrB,GAAG,KAAK,MAAM,EAAE;IAClB,MAAM,IAAIH,oBAAW,CAACE,mBAAmB,CAACC,GAAG,CAAC,EAAEnB,QAAQ,CAAC;EAC3D;EAEA,MAAM4C,IAAkB,GAAG;IACzBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAEtB,MAAGA,CAAC,CAACuB,QAAQ,CAAClD,QAAQ,CAAC;IACvCmD,OAAO,EAAE,CACP,CACEC,WAAW,CAACpD,QAAQ,CAAC,EAAAqD,MAAA,CAAAC,MAAA;MAEnBC,qBAAqB,EAAE,IAAI;MAC3BC,kBAAkB,EAAE;IAAI,GACgB;MAAEC,kBAAkB,EAAE;IAAK,CAAC,EAEvE;EAEL,CAAC;EAED,IAAIC,OAAqC,GAAG,SAAAA,CAAUC,CAAC,EAAEC,QAAQ,EAAE;IAEjE,IAAIF,OAAO,IAAIE,QAAQ,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;MACxC,IAAI;QAEF,OAAOF,CAAC,CAACG,QAAQ,CACf,IAAAC,gCAAiB,EAACH,QAAQ,EAAAP,MAAA,CAAAC,MAAA,KACrBV,IAAI;UACPgB;QAAQ,EACT,CAAC,CAACzB,IAAI,EACPyB,QACF,CAAC;MACH,CAAC,CAAC,OAAOI,KAAK,EAAE;QAGd,MAAMC,WAAW,GAAG5G,OAAO,CAAC,uCAAuC,CAAC;QACpE,IAAImC,QAAKA,CAAC,CAAC0E,EAAE,CAACD,WAAW,CAACE,OAAO,EAAE,QAAQ,CAAC,EAAE;UAC5CC,OAAO,CAACJ,KAAK,CACX,4FACF,CAAC;QACH;QACA,MAAMA,KAAK;MACb;IACF;IACA,OAAO3G,OAAO,CAACsF,UAAU,CAAC,KAAK,CAAC,CAACgB,CAAC,EAAEC,QAAQ,CAAC;EAC/C,CAAC;EACDvG,OAAO,CAACsF,UAAU,CAACxB,GAAG,CAAC,GAAGuC,OAAO;EAEjC,IAAI;IACF,OAAOlB,QAAQ,CAAC,CAAC;EACnB,CAAC,SAAS;IACR,IAAInF,OAAO,CAACsF,UAAU,CAACxB,GAAG,CAAC,KAAKuC,OAAO,EAAE,OAAOrG,OAAO,CAACsF,UAAU,CAACxB,GAAG,CAAC;IACvEuC,OAAO,GAAGhD,SAAS;EACrB;AACF;AAEA,SAAS0C,WAAWA,CAACpD,QAAgB,EAAE;EACrC,IAAI;IAEF,OAAO3C,OAAO,CAAC,0BAA0B,CAAC;EAC5C,CAAC,CAAC,OAAO2G,KAAK,EAAE;IACd,IAAIA,KAAK,CAAC7B,IAAI,KAAK,kBAAkB,EAAE,MAAM6B,KAAK;IAElD,IAAIK,OAAO,GACT,yIAAyI;IAExG;MACjC,IAAI3E,OAAO,CAACC,QAAQ,CAAC2E,GAAG,EAAE;QAGxBD,OAAO,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;MACK;IACF;IAEA,MAAM,IAAIrD,oBAAW,CAACqD,OAAO,EAAErE,QAAQ,CAAC;EAC1C;AACF;AAAC", "ignoreList": []}